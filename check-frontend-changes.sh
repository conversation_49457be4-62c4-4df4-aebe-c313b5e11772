#!/bin/bash

# Script to check if frontend changes are needed for Deepgram and TTS fixes
# This script analyzes the frontend code to determine if any updates are required

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_info "🔍 Checking if frontend changes are needed for Deepgram and TTS fixes..."

# Check if frontend directory exists
FRONTEND_DIRS=("./chat-frontend" "./frontend" "./client" "./web")
FRONTEND_PATH=""

for dir in "${FRONTEND_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        FRONTEND_PATH="$dir"
        break
    fi
done

if [ -z "$FRONTEND_PATH" ]; then
    log_warning "Frontend directory not found in current location"
    log_info "This is normal if frontend is in a separate repository"
    log_info "Backend fixes are independent and don't require frontend changes"
    exit 0
fi

log_info "📁 Found frontend directory: $FRONTEND_PATH"

# Check for audio/WebM handling in frontend
log_info "🎤 Checking audio recording configuration..."

WEBM_FILES=$(find "$FRONTEND_PATH" -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" | xargs grep -l "audio/webm\|MediaRecorder" 2>/dev/null || true)

if [ -n "$WEBM_FILES" ]; then
    log_info "📋 Found audio recording files:"
    echo "$WEBM_FILES" | while read -r file; do
        echo "   • $file"
    done
    
    # Check if proper codec is used
    OPUS_CODEC_FOUND=$(echo "$WEBM_FILES" | xargs grep -l "codecs=opus" 2>/dev/null || true)
    
    if [ -n "$OPUS_CODEC_FOUND" ]; then
        log_success "Frontend already uses correct audio/webm;codecs=opus format"
    else
        log_warning "Frontend might need codec specification update"
        log_info "Recommended: Use 'audio/webm;codecs=opus' for MediaRecorder"
    fi
else
    log_info "No audio recording code found in frontend"
fi

# Check for TTS audio handling
log_info "🔊 Checking TTS audio playback configuration..."

TTS_FILES=$(find "$FRONTEND_PATH" -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" | xargs grep -l "audio.*play\|Audio\|HTMLAudioElement" 2>/dev/null || true)

if [ -n "$TTS_FILES" ]; then
    log_info "📋 Found audio playback files:"
    echo "$TTS_FILES" | while read -r file; do
        echo "   • $file"
    done
    log_success "Frontend audio playback should work with improved backend TTS"
else
    log_info "No audio playback code found in frontend"
fi

# Check for WebSocket handling
log_info "🔌 Checking WebSocket communication..."

WS_FILES=$(find "$FRONTEND_PATH" -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" | xargs grep -l "WebSocket\|ws://" 2>/dev/null || true)

if [ -n "$WS_FILES" ]; then
    log_success "Frontend has WebSocket communication (compatible with backend fixes)"
else
    log_info "No WebSocket code found in frontend"
fi

# Summary and recommendations
log_info ""
log_info "📊 Analysis Summary:"
log_info "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

log_success "✅ Backend fixes are independent and don't require frontend changes"
log_info "   • Deepgram configuration fixes are server-side only"
log_info "   • TTS audio improvements are handled by backend"
log_info "   • Frontend will automatically benefit from these fixes"

log_info ""
log_info "🎯 Recommended actions:"
log_info "   1. Deploy backend fixes to Oracle VM (no frontend changes needed)"
log_info "   2. Test the application after backend deployment"
log_info "   3. If audio recording issues persist, check MediaRecorder codec settings"

log_info ""
log_success "🎉 No frontend deployment required for these fixes!"
