#!/bin/bash

# Test script to verify Oracle VM deployment fixes
# Tests both Deepgram transcription and TTS audio generation

set -e

# Configuration
ORACLE_VM_IP="*************"
ORACLE_USER="ubuntu"
SSH_KEY_PATH="./ssh-key-2025-07-16 (3).key"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_info "🧪 Testing Oracle VM deployment fixes..."

# Test 1: Check if server is running
log_info "📡 Test 1: Server status check"
SERVER_STATUS=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "curl -s http://localhost:3000/health" || echo "ERROR")

if [[ "$SERVER_STATUS" == *"healthy"* ]]; then
    log_success "Server is running and healthy"
else
    log_error "Server health check failed: $SERVER_STATUS"
fi

# Test 2: Check TTS configuration
log_info "🔊 Test 2: TTS configuration check"
TTS_STATUS=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "curl -s http://localhost:3000/api/tts/status")

if [[ "$TTS_STATUS" == *"operational"* ]]; then
    log_success "TTS service is operational"
    echo "   Status: $TTS_STATUS"
else
    log_warning "TTS service status: $TTS_STATUS"
fi

# Test 3: Check if our fixed files are in place
log_info "📁 Test 3: Verify deployed files"
FILE_CHECK=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "ls -la /home/<USER>/chat-server/server.js /home/<USER>/chat-server/src/tts.js 2>/dev/null | wc -l")

if [ "$FILE_CHECK" -eq 2 ]; then
    log_success "Fixed files are deployed correctly"
else
    log_error "Some files are missing from deployment"
fi

# Test 4: Check file timestamps (should be recent)
log_info "⏰ Test 4: Check file modification times"
ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "ls -la /home/<USER>/chat-server/server.js /home/<USER>/chat-server/src/tts.js" | while read line; do
    echo "   $line"
done

# Test 5: Check PM2 process
log_info "🔄 Test 5: PM2 process status"
PM2_STATUS=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null || echo 'unknown'")

if [ "$PM2_STATUS" = "online" ]; then
    log_success "PM2 voice-chat process is online"
else
    log_warning "PM2 process status: $PM2_STATUS"
fi

# Test 6: Check recent logs for our improvements
log_info "📋 Test 6: Check recent server logs"
log_info "Looking for TTS and Deepgram activity..."

RECENT_LOGS=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "pm2 logs voice-chat --lines 20 --nostream" 2>/dev/null || echo "Could not fetch logs")

# Look for TTS activity
if [[ "$RECENT_LOGS" == *"Using Piper TTS"* ]]; then
    log_success "Piper TTS is being used"
elif [[ "$RECENT_LOGS" == *"TTS"* ]]; then
    log_warning "TTS activity detected but not using Piper"
else
    log_info "No recent TTS activity in logs"
fi

# Look for Deepgram activity
if [[ "$RECENT_LOGS" == *"Transcribed:"* ]]; then
    log_success "Deepgram transcription is working"
elif [[ "$RECENT_LOGS" == *"Deepgram error"* ]]; then
    log_warning "Deepgram errors detected - may need audio format investigation"
else
    log_info "No recent Deepgram activity in logs"
fi

# Test 7: Test WebSocket endpoint
log_info "🔌 Test 7: WebSocket endpoint test"
WS_TEST=$(ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" "curl -s -I http://localhost:3000/ws" || echo "ERROR")

if [[ "$WS_TEST" == *"101"* ]] || [[ "$WS_TEST" == *"Upgrade"* ]]; then
    log_success "WebSocket endpoint is accessible"
else
    log_info "WebSocket endpoint response: $(echo "$WS_TEST" | head -1)"
fi

# Summary
log_info ""
log_info "📊 Test Summary:"
log_info "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

log_success "✅ Deployment verification completed"
log_info "   • Server is running and accessible"
log_info "   • Fixed files are deployed to correct location"
log_info "   • PM2 process is managing the service"

log_info ""
log_info "🎯 Next steps for manual testing:"
log_info "   1. Open: https://voice-chat.hotovo.ai"
log_info "   2. Test voice recording and transcription"
log_info "   3. Test TTS audio playback"
log_info "   4. Verify multiple voice message rounds work"

log_info ""
log_info "🔍 For debugging, check logs with:"
log_info "   ssh -i $SSH_KEY_PATH $ORACLE_USER@$ORACLE_VM_IP 'pm2 logs voice-chat --lines 50'"
