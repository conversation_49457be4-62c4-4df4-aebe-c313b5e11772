#!/bin/bash

# Deployment script for Oracle VM - Deepgram and TTS fixes
# This script uploads the fixed files to Oracle VM and restarts the service

set -e

# Configuration
ORACLE_VM_IP="*************"
ORACLE_USER="ubuntu"
REMOTE_PATH="/home/<USER>/chat-server"
LOCAL_BACKEND_PATH="./chat-backend"
SSH_KEY_PATH="./ssh-key-2025-07-16 (3).key"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "$LOCAL_BACKEND_PATH" ]; then
    log_error "Backend directory not found: $LOCAL_BACKEND_PATH"
    log_info "Please run this script from the project root directory"
    exit 1
fi

# Check if SSH key exists
if [ ! -f "$SSH_KEY_PATH" ]; then
    log_error "SSH key not found: $SSH_KEY_PATH"
    exit 1
fi

# Set proper SSH key permissions
chmod 600 "$SSH_KEY_PATH"

log_info "🚀 Starting deployment of Deepgram and TTS fixes to Oracle VM"
log_info "📡 Target: $ORACLE_USER@$ORACLE_VM_IP:$REMOTE_PATH"

# Create temporary directory for deployment files
TEMP_DIR=$(mktemp -d)
log_info "📁 Created temporary directory: $TEMP_DIR"

# Copy fixed files to temp directory
log_info "📋 Preparing files for deployment..."

# Backend files
cp "$LOCAL_BACKEND_PATH/server.js" "$TEMP_DIR/"
cp "$LOCAL_BACKEND_PATH/src/tts.js" "$TEMP_DIR/"
cp "$LOCAL_BACKEND_PATH/test-deepgram-opus.js" "$TEMP_DIR/"

log_success "Files prepared in $TEMP_DIR"

# Show what we're deploying
log_info "📦 Files to deploy:"
ls -la "$TEMP_DIR"

# Create deployment archive
ARCHIVE_NAME="oracle-voice-chat-fixes-$(date +%Y%m%d-%H%M%S).tar.gz"
cd "$TEMP_DIR"
tar -czf "$ARCHIVE_NAME" *
cd - > /dev/null

log_success "Created deployment archive: $TEMP_DIR/$ARCHIVE_NAME"

# Upload to Oracle VM
log_info "⬆️  Uploading files to Oracle VM..."
scp -i "$SSH_KEY_PATH" "$TEMP_DIR/$ARCHIVE_NAME" "$ORACLE_USER@$ORACLE_VM_IP:/tmp/"

if [ $? -eq 0 ]; then
    log_success "Files uploaded successfully"
else
    log_error "Failed to upload files"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Execute deployment on Oracle VM
log_info "🔧 Executing deployment on Oracle VM..."

ssh -i "$SSH_KEY_PATH" "$ORACLE_USER@$ORACLE_VM_IP" << EOF
    set -e
    
    echo "📁 Extracting files..."
    cd /tmp
    tar -xzf "$ARCHIVE_NAME"
    
    echo "🔄 Backing up current files..."
    sudo cp "$REMOTE_PATH/server.js" "$REMOTE_PATH/server.js.backup-\$(date +%Y%m%d-%H%M%S)" || true
    sudo cp "$REMOTE_PATH/src/tts.js" "$REMOTE_PATH/src/tts.js.backup-\$(date +%Y%m%d-%H%M%S)" || true
    sudo cp "$REMOTE_PATH/test-deepgram-opus.js" "$REMOTE_PATH/test-deepgram-opus.js.backup-\$(date +%Y%m%d-%H%M%S)" || true
    
    echo "📝 Applying fixes..."
    sudo cp server.js "$REMOTE_PATH/"
    sudo cp tts.js "$REMOTE_PATH/src/"
    sudo cp test-deepgram-opus.js "$REMOTE_PATH/"
    
    echo "🔐 Setting permissions..."
    sudo chown -R ubuntu:ubuntu "$REMOTE_PATH"
    sudo chmod +x "$REMOTE_PATH/server.js"
    
    echo "🔄 Restarting voice-chat service..."
    sudo systemctl restart voice-chat || pm2 restart voice-chat || echo "⚠️  Could not restart service automatically"
    
    echo "🧹 Cleaning up..."
    rm -f /tmp/"$ARCHIVE_NAME" server.js tts.js test-deepgram-opus.js
    
    echo "✅ Deployment completed successfully!"
EOF

if [ $? -eq 0 ]; then
    log_success "Deployment completed successfully on Oracle VM"
else
    log_error "Deployment failed on Oracle VM"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Clean up local temp directory
rm -rf "$TEMP_DIR"
log_success "Cleaned up temporary files"

log_info "🎉 Deployment Summary:"
log_info "   • Fixed Deepgram configuration (removed problematic parameters)"
log_info "   • Enhanced TTS audio generation (better volume, encoding)"
log_info "   • Improved Piper TTS text handling (UTF-8 + newline)"
log_info ""
log_info "🧪 Next steps:"
log_info "   1. Test Deepgram transcription (multiple voice messages)"
log_info "   2. Test TTS audio playback (should be audible now)"
log_info "   3. Check server logs: ssh -i $SSH_KEY_PATH $ORACLE_USER@$ORACLE_VM_IP 'sudo journalctl -u voice-chat -f'"
log_info ""
log_success "🚀 Oracle VM deployment completed!"
